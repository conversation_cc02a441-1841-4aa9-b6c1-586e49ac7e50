package com.tyme.gotyme.otp.config.featuremanagement;

import com.launchdarkly.sdk.LDContext;
import com.launchdarkly.sdk.server.Components;
import com.launchdarkly.sdk.server.LDClient;
import com.launchdarkly.sdk.server.LDConfig;
import com.launchdarkly.sdk.server.integrations.FileData;
import jakarta.annotation.PreDestroy;
import java.io.IOException;
import java.nio.file.Paths;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;

@Log4j2
@Configuration
@RequiredArgsConstructor
public class LaunchDarklyConfiguration {

  private static final String SERVICE_VERSION = "serviceVersion";
g
  private static final String CLASSPATH_LAUNCHDARKLY_FEATURE_FLAGS_LOCAL = "classpath:launchdarkly/feature-flags-local.json";

  private LDClient ldClient;

  private final ApplicationContext context;

  @Bean
  public LDClient launchDarklyClient(LaunchDarklyProperties properties,
      @Value("${spring.profiles.active}") String activeProfile) {
    // For more configuration ref: https://launchdarkly.github.io/java-core/lib/sdk/server/com/launchdarkly/sdk/server/LDConfig.Builder.html
    if (activeProfile.equals("local")) {
      ldClient = this.localLdClient(properties);
    } else {
      ldClient = new LDClient(properties.getSdkKey());
    }
    if (ldClient.isInitialized()) {
      log.info("LaunchDarkly SDK SUCCESSFULLY successfully initialized");
    } else {
      log.error("LaunchDarkly SDK FAILED to initialize");
    }

    return this.ldClient;
  }

  private LDClient localLdClient(LaunchDarklyProperties properties) {
    var configBuilder = new LDConfig.Builder().events(Components.noEvents());
    try {
      Resource resource = context.getResource(CLASSPATH_LAUNCHDARKLY_FEATURE_FLAGS_LOCAL);
      configBuilder.dataSource(FileData.dataSource().filePaths(Paths.get(resource.getURI())));
    } catch (IOException e) {
      log.warn(
          "Could not load feature flag local launch darkly file, feature flags will not apply");
    }
    return new LDClient(properties.getSdkKey(), configBuilder.build());
  }

  @Bean
  public LDContext ldContext(LaunchDarklyProperties properties) {
    return LDContext.builder(properties.getUser())
        .set(SERVICE_VERSION, properties.getAppVersion())
        .build();
  }

  /**
   * Here we ensure that when the application terminates, the SDK shuts down cleanly and has a
   * chance to deliver analytics events to LaunchDarkly. If analytics events are not delivered, the
   * context attributes and flag usage statistics may not appear on the dashboard. In a normal
   * long-running application, the SDK would continue running and events would be delivered
   * automatically in the background.
   */
  @PreDestroy
  public void destroy() throws IOException {
    this.ldClient.close();
  }
}
